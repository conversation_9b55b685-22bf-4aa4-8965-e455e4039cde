# 快速开始指南

## 🚀 一键部署

### Linux环境部署

```bash
# 1. 下载脚本到Linux服务器
# 2. 设置执行权限
chmod +x *.sh

# 3. 运行自动安装脚本
./install.sh

# 4. 开始监控
./network_monitor.sh
```

### WSL环境部署

如果您在Windows上使用WSL：

```bash
# 1. 进入WSL
wsl

# 2. 切换到脚本目录
cd /mnt/d/vscode/zabbix监控丢包触发mtr探测脚本

# 3. 设置权限并安装
chmod +x *.sh
./install.sh
```

## 📋 使用示例

### 基本监控
```bash
# 查看帮助
./network_monitor.sh --help

# 单次检测
./network_monitor.sh --once

# 持续监控（默认配置）
./network_monitor.sh

# 后台运行
nohup ./network_monitor.sh > /dev/null 2>&1 &
```

### 自定义配置
```bash
# 设置丢包阈值为5%，监控间隔30秒
./network_monitor.sh -t 5 -i 30

# 设置ping包数量为20
./network_monitor.sh -c 20 --once

# 组合参数
./network_monitor.sh -t 1 -i 10 -c 15
```

## 🔧 配置修改

编辑 `network_monitor.sh` 文件开头的配置区域：

```bash
# 监控的主机列表（添加您要监控的主机）
HOSTS=(
    "*******"           # Google DNS
    "*******"           # Google DNS备用
    "*******"           # Cloudflare DNS
    "***************"   # 114 DNS
    "your-server.com"   # 您的服务器
)

# 丢包率阈值（百分比）
PACKET_LOSS_THRESHOLD=2

# 监控间隔（秒）
MONITOR_INTERVAL=60
```

## 📊 日志查看

```bash
# 实时查看日志
tail -f /var/log/network_monitor.log

# 或者当前目录的日志
tail -f ./network_monitor.log

# 查看最近的MTR报告
grep -A 20 "MTR报告" /var/log/network_monitor.log
```

## 🔄 系统服务

如果使用install.sh安装并创建了系统服务：

```bash
# 启动服务
sudo systemctl start network-monitor

# 停止服务
sudo systemctl stop network-monitor

# 查看状态
sudo systemctl status network-monitor

# 查看日志
sudo journalctl -u network-monitor -f

# 开机自启
sudo systemctl enable network-monitor
```

## 📈 监控集成

### Zabbix集成示例

创建Zabbix用户参数：

```bash
# 在zabbix_agentd.conf中添加
UserParameter=network.packetloss[*],/path/to/network_monitor.sh --once | grep "$1" | grep -oE '[0-9]+%' | grep -oE '[0-9]+'
```

### 定时任务

```bash
# 编辑crontab
crontab -e

# 每小时执行一次检测
0 * * * * /path/to/network_monitor.sh --once

# 每5分钟检测一次，阈值设为1%
*/5 * * * * /path/to/network_monitor.sh -t 1 --once
```

## 🛠️ 故障排除

### 常见问题

**Q: 提示权限不足**
```bash
# 使用sudo运行
sudo ./network_monitor.sh

# 或者修改日志目录权限
sudo chown $USER:$USER /var/log/
```

**Q: mtr命令不存在**
```bash
# CentOS/Rocky
sudo yum install mtr
# 或
sudo dnf install mtr

# Ubuntu/Debian
sudo apt install mtr-tiny
```

**Q: ping失败**
```bash
# 检查网络连接
ping -c 1 *******

# 检查防火墙设置
sudo iptables -L
```

### 调试模式

```bash
# 启用详细输出
bash -x ./network_monitor.sh --once

# 检查特定主机
./network_monitor.sh -t 0 --once  # 阈值设为0%，强制触发MTR
```

## 📝 日志示例

### 正常运行日志
```
[2024-01-15 10:30:15] [INFO] 网络监控脚本启动
[2024-01-15 10:30:15] [INFO] 监控主机: ******* ******* *******
[2024-01-15 10:30:16] [INFO] 主机: *******, 丢包率: 0%, 状态: 正常
[2024-01-15 10:30:18] [INFO] 主机: *******, 丢包率: 1%, 状态: 正常
```

### 异常检测日志
```
[2024-01-15 10:35:20] [ALERT] 主机: *******, 丢包率: 5%, 状态: 异常 - 触发MTR测试
[2024-01-15 10:35:20] [INFO] 开始对 ******* 执行MTR测试...
==================== MTR报告 ====================
主机: *******
时间: 2024-01-15 10:35:25
命令: mtr -r -n -c 30 -w *******
==================================================
HOST: localhost                   Loss%   Snt   Last   Avg  Best  Wrst StDev
  1.|-- ***********                0.0%    30    1.2   1.5   1.0   3.2   0.5
  2.|-- ********                   0.0%    30    5.8   6.2   5.1   8.9   1.2
  3.|-- ************               3.3%    30   15.2  16.8  14.1  25.6   3.4
==================================================
```

## 🎯 高级用法

### 多实例运行
```bash
# 监控不同的主机组
./network_monitor.sh &  # 默认配置
HOSTS=("your-server1.com" "your-server2.com") ./network_monitor.sh &
```

### 结合其他工具
```bash
# 与邮件通知结合
./network_monitor.sh --once | grep "ALERT" && echo "网络异常" | mail -s "Alert" <EMAIL>

# 与Webhook结合
./network_monitor.sh --once | grep "ALERT" && curl -X POST -d "网络丢包异常" https://hooks.slack.com/...
```

## 📞 技术支持

如果遇到问题，请检查：
1. 系统兼容性（支持的Linux发行版）
2. 必要工具是否已安装（ping, mtr）
3. 网络连接是否正常
4. 权限设置是否正确

更多详细信息请参考 `README.md` 文件。
