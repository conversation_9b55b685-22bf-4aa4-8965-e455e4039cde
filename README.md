# 网络丢包监控脚本

## 功能说明
监控网络丢包情况，当丢包率超过阈值时自动执行MTR路径追踪分析网络问题。

## 文件说明
- `network_monitor.sh` - 主监控脚本
- `ipaddr.txt` - 监控主机列表配置文件

## 快速使用

### 1. 配置监控主机
编辑 `ipaddr.txt` 文件，每行一个IP地址或域名：
```
*******
*******
*******
***************
```

### 2. 运行脚本
```bash
# 设置执行权限
chmod +x network_monitor.sh

# 单次检测
./network_monitor.sh --once

# 持续监控
./network_monitor.sh

# 后台运行
nohup ./network_monitor.sh > /dev/null 2>&1 &
```

## 主要参数
- `-t N` - 设置丢包率阈值（默认2%）
- `-i N` - 设置监控间隔（默认60秒）
- `-c N` - 设置ping包数量（默认10个）
- `-f FILE` - 指定配置文件（默认ipaddr.txt）
- `--once` - 只执行一次检测

## 工作原理
1. 读取配置文件中的主机列表
2. 对每个主机执行网络连通性测试：
   - 首先尝试ICMP ping测试
   - 如果ping失败，自动尝试TCP连通性检测（端口53/80/443）
3. 如果丢包率 > 阈值，自动执行MTR路径追踪：
   - ICMP模式：`mtr -r -n -c 30 -w`
   - TCP模式：`mtr --tcp --port 端口 -r -n -c 30 -w`
4. 将结果记录到日志文件

## 日志文件
- 默认位置：`/var/log/network_monitor.log`
- 无权限时：`./network_monitor.log`

## 系统要求
- Linux系统（CentOS 7.9+, Rocky 9.6+, Debian 12+, Ubuntu 24.04+）
- 必需工具：`ping` 和 `mtr`
- 可选工具：`tcpping`（提供TCP连通性检测，增强网络检测能力）

### 安装依赖
```bash
# CentOS/Rocky
yum install iputils mtr
# 或
dnf install iputils mtr

# Debian/Ubuntu
apt install iputils-ping mtr-tiny

# 安装tcpping（可选，增强TCP检测能力）
# CentOS/Rocky: 需要EPEL源
yum install epel-release && yum install tcpping
# Debian/Ubuntu
apt install tcptraceroute
```

## 使用示例
```bash
# 设置阈值为5%，间隔30秒
./network_monitor.sh -t 5 -i 30

# 使用自定义配置文件
./network_monitor.sh -f /path/to/hosts.txt

# 单次检测所有主机
./network_monitor.sh --once
```

## 注意事项
- 建议使用root用户运行脚本
- 脚本会自动适应网络环境：
  - 如果ICMP被阻止，自动尝试TCP连通性检测
  - 根据检测方式选择对应的MTR模式
- 修改配置文件后立即生效，无需重启脚本
