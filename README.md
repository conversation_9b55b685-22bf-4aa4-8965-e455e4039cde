# 网络丢包监控脚本

## 功能说明
监控网络丢包情况，当丢包率超过阈值时自动执行MTR路径追踪分析网络问题。

## 文件说明
- `network_monitor.sh` - 主监控脚本
- `ipaddr.txt` - 监控主机列表配置文件

## 快速使用

### 1. 配置监控主机
编辑 `ipaddr.txt` 文件，每行一个IP地址或域名：
```
*******
*******
*******
***************
```

### 2. 运行脚本
```bash
# 设置执行权限
chmod +x network_monitor.sh

# 单次检测
./network_monitor.sh --once

# 持续监控
./network_monitor.sh

# 后台运行
nohup ./network_monitor.sh > /dev/null 2>&1 &
```

## 主要参数
- `-t N` - 设置丢包率阈值（默认2%）
- `-i N` - 设置监控间隔（默认60秒）
- `-c N` - 设置ping包数量（默认10个）
- `-f FILE` - 指定配置文件（默认ipaddr.txt）
- `--once` - 只执行一次检测

## 工作原理
1. 读取配置文件中的主机列表
2. 对每个主机执行ping测试
3. 如果丢包率 > 阈值，自动执行 `mtr -r -n -c 30 -w` 命令
4. 将结果记录到日志文件

## 日志文件
- 默认位置：`/var/log/network_monitor.log`
- 无权限时：`./network_monitor.log`

## 系统要求
- Linux系统（CentOS 7.9+, Rocky 9.6+, Debian 12+, Ubuntu 24.04+）
- 需要安装：`ping` 和 `mtr` 工具

### 安装依赖
```bash
# CentOS/Rocky
yum install iputils mtr
# 或
dnf install iputils mtr

# Debian/Ubuntu
apt install iputils-ping mtr-tiny
```

## 使用示例
```bash
# 设置阈值为5%，间隔30秒
./network_monitor.sh -t 5 -i 30

# 使用自定义配置文件
./network_monitor.sh -f /path/to/hosts.txt

# 单次检测所有主机
./network_monitor.sh --once
```

## 注意事项
- 建议使用root用户运行脚本
- 确保目标主机允许ICMP ping请求
- 修改配置文件后立即生效，无需重启脚本
