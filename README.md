# 网络丢包监控脚本

这是一个用于监控网络丢包情况的Shell脚本，当检测到丢包率超过设定阈值时，会自动执行MTR路径追踪来分析网络问题。

## 功能特性

- 🔍 **多主机监控**: 支持同时监控多个目标主机
- 📊 **丢包检测**: 使用ping命令检测网络丢包率
- 🛣️ **路径追踪**: 丢包超过阈值时自动执行MTR追踪
- 📝 **详细日志**: 完整记录监控结果和MTR报告
- ⚙️ **灵活配置**: 支持命令行参数和脚本内配置
- 🔄 **持续监控**: 支持循环监控和单次检测模式
- 🖥️ **跨平台**: 兼容多个Linux发行版

## 系统兼容性

已测试支持以下系统：
- CentOS 7.9
- Rocky Linux 9.6
- Debian 12
- Ubuntu 24.04

## 依赖安装

### CentOS/Rocky Linux
```bash
# CentOS 7
sudo yum install iputils mtr

# Rocky 9/CentOS 8+
sudo dnf install iputils mtr
```

### Debian/Ubuntu
```bash
sudo apt update
sudo apt install iputils-ping mtr-tiny
```

## 快速开始

### 1. 下载并设置权限
```bash
chmod +x network_monitor.sh
```

### 2. 基本使用
```bash
# 使用默认配置开始监控
./network_monitor.sh

# 查看帮助信息
./network_monitor.sh --help

# 单次检测模式
./network_monitor.sh --once

# 自定义阈值和间隔
./network_monitor.sh -t 5 -i 30
```

## 配置说明

### 脚本内配置
编辑脚本开头的配置区域：

```bash
# 监控的主机列表
HOSTS=(
    "*******"
    "*******"
    "*******"
    "***************"
)

# 丢包率阈值（百分比）
PACKET_LOSS_THRESHOLD=2

# 监控间隔（秒）
MONITOR_INTERVAL=60
```

### 命令行参数
- `-t, --threshold N`: 设置丢包率阈值（默认2%）
- `-i, --interval N`: 设置监控间隔（默认60秒）
- `-c, --count N`: 设置ping包数量（默认10）
- `-o, --once`: 只执行一次检测
- `-h, --help`: 显示帮助信息

## 日志输出示例

### 正常状态日志
```
[2024-01-15 10:30:15] [INFO] 网络监控脚本启动
[2024-01-15 10:30:15] [INFO] 监控主机: ******* ******* *******
[2024-01-15 10:30:15] [INFO] 丢包率阈值: 2%
[2024-01-15 10:30:16] [INFO] 主机: *******, 丢包率: 0%, 状态: 正常
[2024-01-15 10:30:18] [INFO] 主机: *******, 丢包率: 1%, 状态: 正常
```

### 异常状态日志
```
[2024-01-15 10:35:20] [ALERT] 主机: *******, 丢包率: 5%, 状态: 异常 - 触发MTR测试
[2024-01-15 10:35:20] [INFO] 开始对 ******* 执行MTR测试...
==================== MTR报告 ====================
主机: *******
时间: 2024-01-15 10:35:25
命令: mtr -r -n -c 30 -w *******
==================================================
Start: 2024-01-15T10:35:20+0800
HOST: localhost                   Loss%   Snt   Last   Avg  Best  Wrst StDev
  1.|-- ***********                0.0%    30    1.2   1.5   1.0   3.2   0.5
  2.|-- ********                   0.0%    30    5.8   6.2   5.1   8.9   1.2
  3.|-- ************               3.3%    30   15.2  16.8  14.1  25.6   3.4
  4.|-- *************              6.7%    30   28.4  29.1  26.8  35.2   2.8
  5.|-- *******                    0.0%    30   32.1  33.5  31.2  38.9   2.1
==================================================
[2024-01-15 10:35:25] [INFO] MTR测试完成: *******
```

## 使用场景

### 1. 服务器网络监控
```bash
# 后台运行监控
nohup ./network_monitor.sh > /dev/null 2>&1 &
```

### 2. 定时检测
```bash
# 添加到crontab，每小时执行一次单次检测
0 * * * * /path/to/network_monitor.sh --once
```

### 3. 故障排查
```bash
# 降低阈值，增加检测频率
./network_monitor.sh -t 1 -i 10
```

## 注意事项

1. **权限要求**: MTR命令可能需要root权限，建议使用sudo运行
2. **日志位置**: 默认日志保存在`/var/log/network_monitor.log`，如无写权限则保存在当前目录
3. **网络环境**: 确保目标主机允许ICMP ping请求
4. **资源消耗**: MTR测试会消耗一定网络带宽，请根据实际情况调整测试频率

## 故障排除

### 常见问题

**Q: 提示"mtr: command not found"**
A: 请按照上述依赖安装章节安装mtr工具

**Q: ping测试失败**
A: 检查网络连接和防火墙设置，确保目标主机允许ICMP请求

**Q: 无法写入日志文件**
A: 检查日志目录权限，或使用sudo运行脚本

**Q: MTR结果显示不完整**
A: 某些网络设备可能阻止ICMP或UDP包，这是正常现象

## 自定义扩展

脚本支持以下扩展：
- 修改HOSTS数组添加更多监控目标
- 调整ping和mtr的参数
- 添加邮件或webhook通知功能
- 集成到监控系统（如Zabbix、Nagios）

## 许可证

本脚本基于MIT许可证开源，可自由使用和修改。
