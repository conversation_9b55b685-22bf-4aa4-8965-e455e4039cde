#!/bin/bash

# 网络监控脚本演示测试
# 用于展示脚本功能和日志输出

echo "=== 网络丢包监控脚本演示 ==="
echo

# 检查脚本是否存在
if [ ! -f "network_monitor.sh" ]; then
    echo "错误: 找不到 network_monitor.sh 脚本文件"
    exit 1
fi

# 确保脚本有执行权限
chmod +x network_monitor.sh

echo "1. 检查依赖工具..."
echo "检查ping命令:"
if command -v ping >/dev/null 2>&1; then
    echo "✓ ping 命令可用"
    ping -c 1 -W 1 ******* >/dev/null 2>&1 && echo "✓ 网络连接正常" || echo "⚠ 网络连接可能有问题"
else
    echo "✗ ping 命令不可用"
fi

echo
echo "检查mtr命令:"
if command -v mtr >/dev/null 2>&1; then
    echo "✓ mtr 命令可用"
else
    echo "⚠ mtr 命令不可用，需要安装:"
    echo "  CentOS/Rocky: sudo yum install mtr 或 sudo dnf install mtr"
    echo "  Debian/Ubuntu: sudo apt install mtr-tiny"
fi

echo
echo "2. 显示脚本帮助信息..."
echo "----------------------------------------"
./network_monitor.sh --help
echo "----------------------------------------"

echo
echo "3. 检查配置文件..."
if [ -f "ipaddr.txt" ]; then
    echo "✓ 配置文件 ipaddr.txt 存在"
    echo "配置文件内容:"
    echo "----------------------------------------"
    head -5 ipaddr.txt
    echo "----------------------------------------"
else
    echo "⚠ 配置文件 ipaddr.txt 不存在，请先创建"
fi

echo
echo "4. 执行单次检测演示..."
echo "执行命令: ./network_monitor.sh --once"
echo "----------------------------------------"
./network_monitor.sh --once
echo "----------------------------------------"

echo
echo "5. 查看生成的日志文件..."
if [ -f "network_monitor.log" ]; then
    echo "日志文件内容:"
    echo "----------------------------------------"
    cat network_monitor.log
    echo "----------------------------------------"
elif [ -f "/var/log/network_monitor.log" ]; then
    echo "日志文件内容 (/var/log/network_monitor.log):"
    echo "----------------------------------------"
    tail -20 /var/log/network_monitor.log
    echo "----------------------------------------"
else
    echo "未找到日志文件"
fi

echo
echo "6. 演示自定义参数..."
echo "执行命令: ./network_monitor.sh -t 1 -c 5 --once"
echo "（设置阈值为1%，ping包数量为5）"
echo "----------------------------------------"
./network_monitor.sh -t 1 -c 5 --once
echo "----------------------------------------"

echo
echo "7. 演示自定义配置文件..."
echo "创建临时配置文件 test_hosts.txt"
cat > test_hosts.txt << 'EOF'
*******
*******
EOF

echo "执行命令: ./network_monitor.sh -f test_hosts.txt --once"
echo "----------------------------------------"
./network_monitor.sh -f test_hosts.txt --once
echo "----------------------------------------"

# 清理临时文件
rm -f test_hosts.txt

echo
echo "=== 演示完成 ==="
echo
echo "要开始持续监控，请运行:"
echo "  ./network_monitor.sh"
echo
echo "要后台运行监控，请使用:"
echo "  nohup ./network_monitor.sh > /dev/null 2>&1 &"
echo
echo "要停止后台监控，请使用:"
echo "  pkill -f network_monitor.sh"
