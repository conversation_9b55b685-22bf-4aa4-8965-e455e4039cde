#!/bin/bash

# 网络监控脚本安装程序
# 自动检测系统类型并安装必要依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message "$GREEN" "✓ $1"
}

print_warning() {
    print_message "$YELLOW" "⚠ $1"
}

print_error() {
    print_message "$RED" "✗ $1"
}

print_info() {
    print_message "$BLUE" "ℹ $1"
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VERSION=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VERSION=$(lsb_release -sr)
    elif [ -f /etc/redhat-release ]; then
        OS="CentOS"
        VERSION=$(cat /etc/redhat-release | grep -oE '[0-9]+\.[0-9]+')
    else
        OS=$(uname -s)
        VERSION=$(uname -r)
    fi
    
    print_info "检测到操作系统: $OS $VERSION"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -eq 0 ]; then
        print_warning "检测到root用户，将直接安装系统包"
        return 0
    else
        print_info "非root用户，某些操作可能需要sudo权限"
        return 1
    fi
}

# 安装依赖包
install_dependencies() {
    local is_root=$1
    local install_cmd=""
    local packages=""
    
    case "$OS" in
        *"CentOS"*|*"Red Hat"*|*"Rocky"*|*"AlmaLinux"*)
            if command -v dnf >/dev/null 2>&1; then
                install_cmd="dnf install -y"
            else
                install_cmd="yum install -y"
            fi
            packages="iputils mtr"
            ;;
        *"Ubuntu"*|*"Debian"*)
            install_cmd="apt update && apt install -y"
            packages="iputils-ping mtr-tiny"
            ;;
        *"SUSE"*|*"openSUSE"*)
            install_cmd="zypper install -y"
            packages="iputils mtr"
            ;;
        *"Arch"*)
            install_cmd="pacman -S --noconfirm"
            packages="iputils mtr"
            ;;
        *)
            print_warning "未识别的操作系统，请手动安装 ping 和 mtr 工具"
            return 1
            ;;
    esac
    
    print_info "准备安装依赖包: $packages"
    
    if [ "$is_root" -eq 0 ]; then
        eval "$install_cmd $packages"
    else
        print_info "需要sudo权限安装系统包"
        eval "sudo $install_cmd $packages"
    fi
}

# 检查工具是否已安装
check_tools() {
    local missing_tools=()
    
    if ! command -v ping >/dev/null 2>&1; then
        missing_tools+=("ping")
    fi
    
    if ! command -v mtr >/dev/null 2>&1; then
        missing_tools+=("mtr")
    fi
    
    if [ ${#missing_tools[@]} -eq 0 ]; then
        print_success "所有必要工具已安装"
        return 0
    else
        print_warning "缺少工具: ${missing_tools[*]}"
        return 1
    fi
}

# 设置脚本权限
setup_permissions() {
    if [ -f "network_monitor.sh" ]; then
        chmod +x network_monitor.sh
        print_success "设置 network_monitor.sh 执行权限"
    else
        print_error "找不到 network_monitor.sh 文件"
        return 1
    fi
    
    if [ -f "demo_test.sh" ]; then
        chmod +x demo_test.sh
        print_success "设置 demo_test.sh 执行权限"
    fi
}

# 创建系统服务（可选）
create_systemd_service() {
    local create_service=""
    
    echo
    read -p "是否创建systemd服务以便开机自启动? (y/N): " create_service
    
    if [[ "$create_service" =~ ^[Yy]$ ]]; then
        local service_file="/etc/systemd/system/network-monitor.service"
        local script_path=$(realpath network_monitor.sh)
        
        cat > /tmp/network-monitor.service << EOF
[Unit]
Description=Network Packet Loss Monitor
After=network.target

[Service]
Type=simple
User=root
ExecStart=$script_path
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
        
        if [ "$EUID" -eq 0 ]; then
            mv /tmp/network-monitor.service "$service_file"
        else
            sudo mv /tmp/network-monitor.service "$service_file"
        fi
        
        if [ "$EUID" -eq 0 ]; then
            systemctl daemon-reload
            systemctl enable network-monitor.service
        else
            sudo systemctl daemon-reload
            sudo systemctl enable network-monitor.service
        fi
        
        print_success "systemd服务已创建并启用"
        print_info "使用以下命令管理服务:"
        print_info "  启动: sudo systemctl start network-monitor"
        print_info "  停止: sudo systemctl stop network-monitor"
        print_info "  状态: sudo systemctl status network-monitor"
        print_info "  日志: sudo journalctl -u network-monitor -f"
    fi
}

# 运行测试
run_test() {
    local run_test=""
    
    echo
    read -p "是否运行测试以验证安装? (Y/n): " run_test
    
    if [[ ! "$run_test" =~ ^[Nn]$ ]]; then
        print_info "运行安装测试..."
        if [ -f "demo_test.sh" ]; then
            ./demo_test.sh
        else
            print_info "运行单次检测测试..."
            ./network_monitor.sh --once
        fi
    fi
}

# 主安装流程
main() {
    print_info "开始安装网络监控脚本..."
    echo
    
    # 检测系统
    detect_os
    
    # 检查root权限
    check_root
    local is_root=$?
    
    echo
    
    # 检查工具是否已安装
    if ! check_tools; then
        print_info "开始安装缺少的工具..."
        if install_dependencies $is_root; then
            print_success "依赖安装完成"
        else
            print_error "依赖安装失败"
            exit 1
        fi
        
        # 再次检查
        if ! check_tools; then
            print_error "安装后仍然缺少必要工具，请手动安装"
            exit 1
        fi
    fi
    
    echo
    
    # 设置权限
    setup_permissions
    
    echo
    
    # 创建systemd服务（可选）
    if command -v systemctl >/dev/null 2>&1; then
        create_systemd_service
    fi
    
    # 运行测试
    run_test
    
    echo
    print_success "安装完成！"
    echo
    print_info "使用方法:"
    print_info "  查看帮助: ./network_monitor.sh --help"
    print_info "  单次检测: ./network_monitor.sh --once"
    print_info "  持续监控: ./network_monitor.sh"
    print_info "  后台运行: nohup ./network_monitor.sh > /dev/null 2>&1 &"
    echo
    print_info "配置文件: 编辑 network_monitor.sh 开头的配置区域"
    print_info "日志文件: /var/log/network_monitor.log 或 ./network_monitor.log"
}

# 执行主程序
main "$@"
