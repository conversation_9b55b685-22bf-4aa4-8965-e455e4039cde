#!/bin/bash

# 网络丢包监控脚本
# 支持 CentOS 7.9, Rocky 9.6, Debian 12, Ubuntu 24.04
# 作者: AI Assistant
# 版本: 1.0

# ===================配置区域===================
# 主机配置文件路径
CONFIG_FILE="./ipaddr.txt"

# 丢包率阈值（百分比，不含%符号）
PACKET_LOSS_THRESHOLD=2

# ping测试参数
PING_COUNT=10           # 发送ping包数量
PING_TIMEOUT=5          # ping超时时间（秒）

# mtr测试参数
MTR_COUNT=30            # mtr发送包数量

# 监控间隔（秒）
MONITOR_INTERVAL=60

# 日志文件路径
LOG_DIR="/var/log"
LOG_FILE="${LOG_DIR}/network_monitor.log"

# 如果没有写权限，使用当前目录
if [ ! -w "$LOG_DIR" ]; then
    LOG_FILE="./network_monitor.log"
fi

# ===================函数定义===================

# 检查是否为数字
is_number() {
    [[ $1 =~ ^[0-9]+$ ]]
}

# 日志记录函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# 从配置文件加载主机列表
load_hosts_from_file() {
    local config_file="$1"
    local hosts=()

    # 检查配置文件是否存在
    if [ ! -f "$config_file" ]; then
        log_message "ERROR" "配置文件不存在: $config_file"
        echo "请创建配置文件 $config_file，每行一个IP地址或域名"
        return 1
    fi

    # 读取配置文件，跳过空行
    while IFS= read -r line || [ -n "$line" ]; do
        # 去除行首行尾空格
        line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

        # 跳过空行
        if [[ -n "$line" ]]; then
            hosts+=("$line")
        fi
    done < "$config_file"

    # 检查是否读取到主机
    if [ ${#hosts[@]} -eq 0 ]; then
        log_message "ERROR" "配置文件中没有找到有效的主机地址: $config_file"
        return 1
    fi

    # 输出主机列表
    printf '%s\n' "${hosts[@]}"
    return 0
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=()

    # 检查ping命令
    if ! command -v ping >/dev/null 2>&1; then
        missing_tools+=("ping")
    fi

    # 检查mtr命令
    if ! command -v mtr >/dev/null 2>&1; then
        missing_tools+=("mtr")
    fi

    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_message "ERROR" "缺少必要工具: ${missing_tools[*]}"
        echo "请安装缺少的工具："
        echo "CentOS/Rocky: sudo yum install iputils mtr 或 sudo dnf install iputils mtr"
        echo "Debian/Ubuntu: sudo apt update && sudo apt install iputils-ping mtr-tiny"
        exit 1
    fi

    log_message "INFO" "依赖检查完成，所有必要工具已安装"
}

# 执行ping测试并返回丢包率
ping_test() {
    local host="$1"
    local ping_result
    local packet_loss
    
    # 执行ping命令
    ping_result=$(ping -c "$PING_COUNT" -W "$PING_TIMEOUT" "$host" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        log_message "ERROR" "无法ping通主机: $host"
        echo "-1"  # 返回-1表示ping失败
        return
    fi
    
    # 提取丢包率（更通用的方法）
    packet_loss=$(echo "$ping_result" | grep -oE '[0-9]+%' | head -1 | grep -oE '[0-9]+')

    if [ -z "$packet_loss" ] || ! is_number "$packet_loss"; then
        log_message "ERROR" "无法解析ping结果: $host"
        echo "-1"
        return
    fi
    
    echo "$packet_loss"
}

# 执行MTR测试
run_mtr() {
    local host="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    log_message "INFO" "开始对 $host 执行MTR测试..."
    
    # 执行mtr命令
    local mtr_result
    mtr_result=$(mtr -r -n -c "$MTR_COUNT" -w "$host" 2>&1)
    local mtr_exit_code=$?

    if [ $mtr_exit_code -eq 0 ]; then
        # 记录MTR结果到日志，格式规范
        {
            echo ""
            echo "┌─────────────────────────────────────────────────────────────────────────────┐"
            echo "│                              MTR 路径追踪报告                               │"
            echo "├─────────────────────────────────────────────────────────────────────────────┤"
            printf "│ 目标主机: %-30s 测试时间: %-19s │\n" "$host" "$timestamp"
            printf "│ 执行命令: %-61s │\n" "mtr -r -n -c $MTR_COUNT -w $host"
            echo "├─────────────────────────────────────────────────────────────────────────────┤"
            echo "│ 路径追踪结果:                                                               │"
            echo "└─────────────────────────────────────────────────────────────────────────────┘"
            echo ""
            # 处理MTR输出，确保格式整齐
            echo "$mtr_result" | while IFS= read -r line; do
                if [[ "$line" =~ ^[[:space:]]*$ ]]; then
                    echo ""  # 保留空行
                elif [[ "$line" =~ ^HOST: ]]; then
                    echo "  $line"  # 表头缩进
                else
                    echo "  $line"  # 数据行缩进
                fi
            done
            echo ""
            echo "═══════════════════════════════════════════════════════════════════════════════"
            echo ""
        } >> "$LOG_FILE"

        log_message "INFO" "MTR测试完成: $host"
    else
        log_message "ERROR" "MTR测试失败: $host (退出码: $mtr_exit_code)"
        if echo "$mtr_result" | grep -q "Permission denied\|Operation not permitted"; then
            log_message "WARNING" "MTR可能需要root权限，请尝试使用sudo运行脚本"
        fi
    fi
}

# 监控单个主机
monitor_host() {
    local host="$1"
    local packet_loss
    
    packet_loss=$(ping_test "$host")

    # 检查ping结果是否有效
    if [ "$packet_loss" = "-1" ]; then
        return  # ping失败，跳过此主机
    fi

    # 验证丢包率是否为数字
    if ! is_number "$packet_loss"; then
        log_message "ERROR" "丢包率不是有效数字: $packet_loss"
        return
    fi

    # 记录ping结果
    if [ "$packet_loss" -le "$PACKET_LOSS_THRESHOLD" ]; then
        log_message "INFO" "主机: $host, 丢包率: ${packet_loss}%, 状态: 正常"
    else
        log_message "ALERT" "主机: $host, 丢包率: ${packet_loss}%, 状态: 异常 - 触发MTR测试"
        run_mtr "$host"
    fi
}

# 信号处理函数
cleanup() {
    log_message "INFO" "收到退出信号，正在停止监控..."
    exit 0
}

# 显示使用帮助
show_help() {
    cat << EOF
网络丢包监控脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -f, --file FILE     指定主机配置文件（默认: $CONFIG_FILE）
    -t, --threshold N   设置丢包率阈值（默认: $PACKET_LOSS_THRESHOLD%）
    -i, --interval N    设置监控间隔（默认: $MONITOR_INTERVAL 秒）
    -c, --count N       设置ping包数量（默认: $PING_COUNT）
    -o, --once          只执行一次检测，不循环监控

示例:
    $0                      # 使用默认配置开始监控
    $0 -f /path/to/hosts.txt # 指定自定义主机配置文件
    $0 -t 5 -i 30           # 设置阈值为5%，间隔30秒
    $0 --once               # 只执行一次检测

配置文件格式:
    每行一个IP地址或域名
    示例:
        *******
        *******
        your-server.com

默认配置文件: $CONFIG_FILE
日志文件位置: $LOG_FILE
EOF
}

# ===================主程序===================

# 解析命令行参数
ONCE_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--file)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -t|--threshold)
            if is_number "$2" && [ "$2" -ge 0 ] && [ "$2" -le 100 ]; then
                PACKET_LOSS_THRESHOLD="$2"
            else
                echo "错误: 阈值必须是0-100之间的数字"
                exit 1
            fi
            shift 2
            ;;
        -i|--interval)
            if is_number "$2" && [ "$2" -gt 0 ]; then
                MONITOR_INTERVAL="$2"
            else
                echo "错误: 监控间隔必须是正整数"
                exit 1
            fi
            shift 2
            ;;
        -c|--count)
            if is_number "$2" && [ "$2" -gt 0 ]; then
                PING_COUNT="$2"
            else
                echo "错误: ping包数量必须是正整数"
                exit 1
            fi
            shift 2
            ;;
        -o|--once)
            ONCE_MODE=true
            shift
            ;;
        *)
            echo "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 检查依赖
check_dependencies

# 加载主机列表
log_message "INFO" "从配置文件加载主机列表: $CONFIG_FILE"
HOSTS_LIST=$(load_hosts_from_file "$CONFIG_FILE")
if [ $? -ne 0 ]; then
    exit 1
fi

# 将主机列表转换为数组（兼容老版本bash）
HOSTS=()
while IFS= read -r line; do
    [ -n "$line" ] && HOSTS+=("$line")
done <<< "$HOSTS_LIST"

# 显示启动信息
log_message "INFO" "网络监控脚本启动"
log_message "INFO" "配置文件: $CONFIG_FILE"
log_message "INFO" "监控主机: ${HOSTS[*]}"
log_message "INFO" "主机数量: ${#HOSTS[@]}"
log_message "INFO" "丢包率阈值: ${PACKET_LOSS_THRESHOLD}%"
log_message "INFO" "监控间隔: ${MONITOR_INTERVAL}秒"
log_message "INFO" "日志文件: $LOG_FILE"

if [ "$ONCE_MODE" = true ]; then
    log_message "INFO" "单次检测模式"
    # 执行一次检测
    for host in "${HOSTS[@]}"; do
        monitor_host "$host"
    done
    log_message "INFO" "单次检测完成"
else
    log_message "INFO" "持续监控模式（按Ctrl+C停止）"
    # 持续监控循环
    while true; do
        for host in "${HOSTS[@]}"; do
            monitor_host "$host"
        done

        log_message "INFO" "等待 ${MONITOR_INTERVAL} 秒后进行下一轮检测..."
        sleep "$MONITOR_INTERVAL"
    done
fi
