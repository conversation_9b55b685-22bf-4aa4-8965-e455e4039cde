#!/bin/bash

# 网络丢包监控脚本
# 支持 CentOS 7.9, <PERSON> 9.6, Debian 12, Ubuntu 24.04
# 作者: AI Assistant
# 版本: 1.0

# ===================配置区域===================
# 监控的主机列表（可添加多个）
HOSTS=(
    "*******"
    "*******"
    "*******"
    "***************"
)

# 丢包率阈值（百分比，不含%符号）
PACKET_LOSS_THRESHOLD=2

# ping测试参数
PING_COUNT=10           # 发送ping包数量
PING_TIMEOUT=5          # ping超时时间（秒）

# mtr测试参数
MTR_COUNT=30            # mtr发送包数量

# 监控间隔（秒）
MONITOR_INTERVAL=60

# 日志文件路径
LOG_DIR="/var/log"
LOG_FILE="${LOG_DIR}/network_monitor.log"

# 如果没有写权限，使用当前目录
if [ ! -w "$LOG_DIR" ]; then
    LOG_FILE="./network_monitor.log"
fi

# ===================函数定义===================

# 日志记录函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=()
    
    # 检查ping命令
    if ! command -v ping >/dev/null 2>&1; then
        missing_tools+=("ping")
    fi
    
    # 检查mtr命令
    if ! command -v mtr >/dev/null 2>&1; then
        missing_tools+=("mtr")
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_message "ERROR" "缺少必要工具: ${missing_tools[*]}"
        echo "请安装缺少的工具："
        echo "CentOS/Rocky: sudo yum install iputils mtr 或 sudo dnf install iputils mtr"
        echo "Debian/Ubuntu: sudo apt update && sudo apt install iputils-ping mtr-tiny"
        exit 1
    fi
    
    log_message "INFO" "依赖检查完成，所有必要工具已安装"
}

# 执行ping测试并返回丢包率
ping_test() {
    local host="$1"
    local ping_result
    local packet_loss
    
    # 执行ping命令
    ping_result=$(ping -c "$PING_COUNT" -W "$PING_TIMEOUT" "$host" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        log_message "ERROR" "无法ping通主机: $host"
        echo "-1"  # 返回-1表示ping失败
        return
    fi
    
    # 提取丢包率（支持中英文输出）
    packet_loss=$(echo "$ping_result" | grep -oE '[0-9]+%.*loss|[0-9]+%.*丢失' | grep -oE '[0-9]+')
    
    if [ -z "$packet_loss" ]; then
        log_message "ERROR" "无法解析ping结果: $host"
        echo "-1"
        return
    fi
    
    echo "$packet_loss"
}

# 执行MTR测试
run_mtr() {
    local host="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    log_message "INFO" "开始对 $host 执行MTR测试..."
    
    # 执行mtr命令
    local mtr_result
    mtr_result=$(mtr -r -n -c "$MTR_COUNT" -w "$host" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        # 记录MTR结果到日志
        {
            echo "==================== MTR报告 ===================="
            echo "主机: $host"
            echo "时间: $timestamp"
            echo "命令: mtr -r -n -c $MTR_COUNT -w $host"
            echo "=================================================="
            echo "$mtr_result"
            echo "=================================================="
            echo ""
        } >> "$LOG_FILE"
        
        log_message "INFO" "MTR测试完成: $host"
    else
        log_message "ERROR" "MTR测试失败: $host"
    fi
}

# 监控单个主机
monitor_host() {
    local host="$1"
    local packet_loss
    
    packet_loss=$(ping_test "$host")
    
    if [ "$packet_loss" -eq -1 ]; then
        return  # ping失败，跳过此主机
    fi
    
    # 记录ping结果
    if [ "$packet_loss" -le "$PACKET_LOSS_THRESHOLD" ]; then
        log_message "INFO" "主机: $host, 丢包率: ${packet_loss}%, 状态: 正常"
    else
        log_message "ALERT" "主机: $host, 丢包率: ${packet_loss}%, 状态: 异常 - 触发MTR测试"
        run_mtr "$host"
    fi
}

# 信号处理函数
cleanup() {
    log_message "INFO" "收到退出信号，正在停止监控..."
    exit 0
}

# 显示使用帮助
show_help() {
    cat << EOF
网络丢包监控脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -t, --threshold N   设置丢包率阈值（默认: $PACKET_LOSS_THRESHOLD%）
    -i, --interval N    设置监控间隔（默认: $MONITOR_INTERVAL 秒）
    -c, --count N       设置ping包数量（默认: $PING_COUNT）
    -o, --once          只执行一次检测，不循环监控

示例:
    $0                  # 使用默认配置开始监控
    $0 -t 5 -i 30       # 设置阈值为5%，间隔30秒
    $0 --once           # 只执行一次检测

监控的主机列表可在脚本开头的HOSTS数组中修改。
日志文件位置: $LOG_FILE
EOF
}

# ===================主程序===================

# 解析命令行参数
ONCE_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--threshold)
            PACKET_LOSS_THRESHOLD="$2"
            shift 2
            ;;
        -i|--interval)
            MONITOR_INTERVAL="$2"
            shift 2
            ;;
        -c|--count)
            PING_COUNT="$2"
            shift 2
            ;;
        -o|--once)
            ONCE_MODE=true
            shift
            ;;
        *)
            echo "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 检查依赖
check_dependencies

# 显示启动信息
log_message "INFO" "网络监控脚本启动"
log_message "INFO" "监控主机: ${HOSTS[*]}"
log_message "INFO" "丢包率阈值: ${PACKET_LOSS_THRESHOLD}%"
log_message "INFO" "监控间隔: ${MONITOR_INTERVAL}秒"
log_message "INFO" "日志文件: $LOG_FILE"

if [ "$ONCE_MODE" = true ]; then
    log_message "INFO" "单次检测模式"
    # 执行一次检测
    for host in "${HOSTS[@]}"; do
        monitor_host "$host"
    done
    log_message "INFO" "单次检测完成"
else
    log_message "INFO" "持续监控模式（按Ctrl+C停止）"
    # 持续监控循环
    while true; do
        for host in "${HOSTS[@]}"; do
            monitor_host "$host"
        done
        
        log_message "INFO" "等待 ${MONITOR_INTERVAL} 秒后进行下一轮检测..."
        sleep "$MONITOR_INTERVAL"
    done
fi
