#!/bin/bash

# 主机配置文件管理工具
# 用于方便地管理 ipaddr.txt 配置文件

CONFIG_FILE="./ipaddr.txt"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 显示帮助信息
show_help() {
    cat << EOF
主机配置文件管理工具

用法: $0 [命令] [参数]

命令:
    list, ls            显示当前配置的主机列表
    add HOST [DESC]     添加主机（可选描述）
    remove, rm HOST     删除指定主机
    edit                使用默认编辑器编辑配置文件
    validate            验证配置文件格式
    backup              备份当前配置文件
    restore FILE        从备份文件恢复配置
    template            创建默认配置文件模板
    test HOST           测试指定主机的连通性
    help                显示此帮助信息

示例:
    $0 list                          # 显示主机列表
    $0 add *********** "内网网关"    # 添加主机
    $0 rm *******                    # 删除主机
    $0 test *******                  # 测试主机连通性
    $0 backup                        # 备份配置文件

配置文件: $CONFIG_FILE
EOF
}

# 确保配置文件存在
ensure_config_file() {
    if [ ! -f "$CONFIG_FILE" ]; then
        print_warning "配置文件不存在，创建默认配置文件"
        create_template
    fi
}

# 创建默认配置文件模板
create_template() {
    cat > "$CONFIG_FILE" << 'EOF'
# 网络监控目标主机配置文件
# 每行一个IP地址或域名，支持注释
# 以#开头的行为注释，空行将被忽略

# Google DNS服务器
*******          # Google DNS Primary
*******          # Google DNS Secondary

# Cloudflare DNS服务器
*******          # Cloudflare DNS Primary

# 国内DNS服务器
***************  # 114 DNS

# 自定义服务器（取消注释后使用）
# your-server.example.com
# ***********
EOF
    print_success "已创建默认配置文件: $CONFIG_FILE"
}

# 显示主机列表
list_hosts() {
    ensure_config_file
    
    print_info "当前配置的主机列表:"
    echo "----------------------------------------"
    
    local count=0
    while IFS= read -r line || [ -n "$line" ]; do
        # 去除行首行尾空格
        line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        
        # 跳过空行和注释行
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # 提取IP地址和注释
        host=$(echo "$line" | sed 's/[[:space:]]*#.*$//')
        host=$(echo "$host" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        comment=$(echo "$line" | grep -o '#.*' | sed 's/^#[[:space:]]*//')
        
        if [ -n "$host" ]; then
            count=$((count + 1))
            if [ -n "$comment" ]; then
                printf "%2d. %-20s # %s\n" "$count" "$host" "$comment"
            else
                printf "%2d. %s\n" "$count" "$host"
            fi
        fi
    done < "$CONFIG_FILE"
    
    echo "----------------------------------------"
    print_info "总计: $count 个主机"
}

# 添加主机
add_host() {
    local host="$1"
    local desc="$2"
    
    if [ -z "$host" ]; then
        print_error "请指定要添加的主机地址"
        return 1
    fi
    
    ensure_config_file
    
    # 检查主机是否已存在
    if grep -q "^[[:space:]]*$host[[:space:]]*" "$CONFIG_FILE"; then
        print_warning "主机 $host 已存在于配置文件中"
        return 1
    fi
    
    # 添加主机到配置文件
    if [ -n "$desc" ]; then
        echo "$host          # $desc" >> "$CONFIG_FILE"
    else
        echo "$host" >> "$CONFIG_FILE"
    fi
    
    print_success "已添加主机: $host"
}

# 删除主机
remove_host() {
    local host="$1"
    
    if [ -z "$host" ]; then
        print_error "请指定要删除的主机地址"
        return 1
    fi
    
    ensure_config_file
    
    # 检查主机是否存在
    if ! grep -q "^[[:space:]]*$host[[:space:]]*" "$CONFIG_FILE"; then
        print_warning "主机 $host 不存在于配置文件中"
        return 1
    fi
    
    # 删除主机
    sed -i "/^[[:space:]]*$host[[:space:]]*/d" "$CONFIG_FILE"
    print_success "已删除主机: $host"
}

# 编辑配置文件
edit_config() {
    ensure_config_file
    
    local editor="${EDITOR:-nano}"
    if command -v "$editor" >/dev/null 2>&1; then
        "$editor" "$CONFIG_FILE"
        print_success "配置文件编辑完成"
    else
        print_error "编辑器 $editor 不可用，请设置 EDITOR 环境变量"
        print_info "可用的编辑器: vi, nano, vim"
    fi
}

# 验证配置文件
validate_config() {
    ensure_config_file
    
    print_info "验证配置文件格式..."
    
    local valid_hosts=0
    local invalid_lines=0
    local line_num=0
    
    while IFS= read -r line || [ -n "$line" ]; do
        line_num=$((line_num + 1))
        
        # 去除行首行尾空格
        line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        
        # 跳过空行和注释行
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # 提取主机地址
        host=$(echo "$line" | sed 's/[[:space:]]*#.*$//')
        host=$(echo "$host" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        
        if [ -n "$host" ]; then
            # 简单验证（检查是否包含空格）
            if [[ "$host" =~ [[:space:]] ]]; then
                print_warning "第 $line_num 行格式可能有问题: $line"
                invalid_lines=$((invalid_lines + 1))
            else
                valid_hosts=$((valid_hosts + 1))
            fi
        fi
    done < "$CONFIG_FILE"
    
    echo "----------------------------------------"
    print_success "有效主机: $valid_hosts"
    if [ $invalid_lines -gt 0 ]; then
        print_warning "可能有问题的行: $invalid_lines"
    else
        print_success "配置文件格式正确"
    fi
}

# 备份配置文件
backup_config() {
    ensure_config_file
    
    local backup_file="${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$CONFIG_FILE" "$backup_file"
    print_success "配置文件已备份到: $backup_file"
}

# 从备份恢复配置
restore_config() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        print_error "请指定备份文件路径"
        return 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        print_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    cp "$backup_file" "$CONFIG_FILE"
    print_success "已从备份文件恢复配置: $backup_file"
}

# 测试主机连通性
test_host() {
    local host="$1"
    
    if [ -z "$host" ]; then
        print_error "请指定要测试的主机地址"
        return 1
    fi
    
    print_info "测试主机连通性: $host"
    
    if ping -c 3 -W 3 "$host" >/dev/null 2>&1; then
        print_success "主机 $host 连通正常"
    else
        print_error "主机 $host 连通失败"
    fi
}

# 主程序
main() {
    case "${1:-help}" in
        list|ls)
            list_hosts
            ;;
        add)
            add_host "$2" "$3"
            ;;
        remove|rm)
            remove_host "$2"
            ;;
        edit)
            edit_config
            ;;
        validate)
            validate_config
            ;;
        backup)
            backup_config
            ;;
        restore)
            restore_config "$2"
            ;;
        template)
            create_template
            ;;
        test)
            test_host "$2"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"
